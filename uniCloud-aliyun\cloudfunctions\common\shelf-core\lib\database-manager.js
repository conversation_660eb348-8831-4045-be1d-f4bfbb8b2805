'use strict'

/**
 * 数据库操作管理器
 * 统一处理所有数据库相关操作，减少重复代码
 */
class DatabaseManager {
  constructor() {
    this.db = uniCloud.database()
    this.dbCmd = this.db.command
  }

  /**
   * 获取平台配置
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型（可选）
   * @param {boolean} activeOnly 是否只获取启用的配置
   * @returns {Promise<Array>} 平台配置列表
   */
  async getPlatformConfigs(userId, platformType = null, activeOnly = true) {
    try {
      const whereCondition = {
        user_id: userId
      }

      if (platformType) {
        whereCondition.platform_type = platformType
      }

      if (activeOnly) {
        whereCondition.status = 1
      }

      const result = await this.db.collection('platform-configs')
        .where(whereCondition)
        .orderBy('create_time', 'desc')
        .get()

      return result.data
    } catch (error) {
      console.error('获取平台配置失败:', error)
      throw error
    }
  }

  /**
   * 获取所有用户的平台配置（用于定时任务）
   * @param {string} platformType 平台类型（可选）
   * @param {boolean} activeOnly 是否只获取启用的配置
   * @returns {Promise<Array>} 平台配置列表
   */
  async getAllPlatformConfigs(platformType = null, activeOnly = true) {
    try {
      // {{ AURA-X: Add - 新增获取所有用户平台配置的方法，用于定时任务. Approval: 寸止(ID:1735373100). }}
      const whereCondition = {}

      if (platformType) {
        whereCondition.platform_type = platformType
      }

      if (activeOnly) {
        whereCondition.status = 1
      }

      const result = await this.db.collection('platform-configs')
        .where(whereCondition)
        .orderBy('create_time', 'desc')
        .get()

      return result.data
    } catch (error) {
      console.error('获取所有平台配置失败:', error)
      throw error
    }
  }

  /**
   * 更新平台配置
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Object} updateData 更新数据
   * @returns {Promise<void>}
   */
  async updatePlatformConfig(userId, platformType, updateData) {
    try {
      await this.db.collection('platform-configs')
        .where({
          user_id: userId,
          platform_type: platformType
        })
        .update({
          ...updateData,
          update_time: new Date()
        })
    } catch (error) {
      console.error('更新平台配置失败:', error)
      throw error
    }
  }

  /**
   * 根据配置ID精确更新平台配置
   * @param {string} configId 配置ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<void>}
   */
  async updatePlatformConfigById(configId, updateData) {
    try {
      // {{ AURA-X: Add - 基于配置ID的精确更新方法，避免误更新多个配置. Approval: 寸止(ID:1735373000). }}
      await this.db.collection('platform-configs')
        .doc(configId)
        .update({
          ...updateData,
          update_time: new Date()
        })
    } catch (error) {
      console.error('根据ID更新平台配置失败:', error)
      throw error
    }
  }

  /**
   * 保存或更新平台配置
   * @param {string} userId 用户ID
   * @param {Object} configData 配置数据
   * @returns {Promise<void>}
   */
  async savePlatformConfig(userId, configData) {
    try {
      const {
        platformType,
        platformName,
        username,
        password,
        token,
        cookie,
        headers,
        autoLogin,
        remark
      } = configData

      // 检查是否已存在相同平台配置
      const existingConfig = await this.db.collection('platform-configs')
        .where({
          user_id: userId,
          platform_type: platformType
        })
        .get()

      const configRecord = {
        user_id: userId,
        platform_name: platformName,
        platform_type: platformType,
        username: username || '',
        password: password || '',
        token: token || '',
        cookie: cookie || '',
        headers: headers || {},
        auto_login: autoLogin || false,
        login_status: 0,
        status: 1,
        remark: remark || '',
        update_time: new Date()
      }

      if (existingConfig.data.length > 0) {
        // 更新现有配置
        await this.db.collection('platform-configs')
          .doc(existingConfig.data[0]._id)
          .update(configRecord)
      } else {
        // 创建新配置
        configRecord.create_time = new Date()
        await this.db.collection('platform-configs').add(configRecord)
      }
    } catch (error) {
      console.error('保存平台配置失败:', error)
      throw error
    }
  }

  /**
   * 获取货架列表
   * @param {string} userId 用户ID
   * @param {Object} queryOptions 查询选项
   * @returns {Promise<Object>} 货架列表和分页信息
   */
  async getShelfList(userId, queryOptions = {}) {
    try {
      const {
        platformType,
        gameAccount,
        status,
        pageIndex = 1,
        pageSize = 20,
        activeOnly = null
      } = queryOptions

      const whereCondition = {
        user_id: userId
      }

      if (platformType) {
        whereCondition.platform_type = platformType
      }

      if (gameAccount) {
        whereCondition.game_account = this.dbCmd.regex({
          regexp: gameAccount,
          options: 'i'
        })
      }

      if (status !== undefined) {
        whereCondition.unified_state = status
      }

      if (activeOnly !== null) {
        whereCondition.is_active = activeOnly
      }

      const shelves = await this.db.collection('account-shelves')
        .where(whereCondition)
        .orderBy('last_sync_time', 'desc')
        .skip((pageIndex - 1) * pageSize)
        .limit(pageSize)
        .get()

      const total = await this.db.collection('account-shelves')
        .where(whereCondition)
        .count()

      return {
        list: shelves.data,
        total: total.total,
        pageIndex,
        pageSize
      }
    } catch (error) {
      console.error('获取货架列表失败:', error)
      throw error
    }
  }

  /**
   * 批量更新或插入货架数据（智能同步版本）
   *
   * 核心功能：
   * 1. 智能去重：自动处理重复的游戏账号数据
   * 2. 增量同步：只更新有变化的数据，提高效率
   * 3. 删除检测：自动删除平台上已不存在的本地记录
   * 4. 性能监控：详细记录各步骤的执行时间
   *
   * 同步流程：
   * 1. 获取数据库中现有的货架记录
   * 2. 清理数据库中的重复记录
   * 3. 对平台返回的数据进行去重处理
   * 4. 批量更新或插入处理后的数据
   * 5. 删除平台上已不存在的孤立记录
   *
   * @param {string} userId - 用户ID
   * @param {string} platformType - 平台类型（如：zuhaowan、uhaozu）
   * @param {Array} shelfList - 从平台获取的货架列表
   * @returns {Promise<Object>} 同步结果统计信息
   */
  async batchUpsertShelves(userId, platformType, shelfList) {
    const syncStartTime = Date.now()

    try {
      console.log(`开始智能同步 ${platformType} 平台货架数据，共 ${shelfList.length} 个货架`)

      // {{ AURA-X: Modify - 简化批量货架操作逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}

      // 步骤1：初始化同步统计信息
      const syncStatistics = this._initializeSyncStats()

      // 步骤2：获取数据库中现有的货架数据
      const existingShelfRecords = await this._fetchExistingShelves(userId, platformType, syncStatistics)

      // 步骤3：执行完整的同步操作流程
      await this._performSyncOperations(userId, platformType, shelfList, existingShelfRecords, syncStatistics)

      // 步骤4：完成统计并输出结果
      syncStatistics.performance.total = Date.now() - syncStartTime
      console.log(`${platformType} 平台同步完成 - 新增:${syncStatistics.created} 更新:${syncStatistics.updated} 删除:${syncStatistics.deleted} 去重:${syncStatistics.duplicatesRemoved} 耗时:${syncStatistics.performance.total}ms`)

      return {
        total: shelfList.length,
        stats: syncStatistics
      }

    } catch (error) {
      console.error(`${platformType} 平台货架批量同步失败:`, error)
      throw error
    }
  }

  /**
   * 更新货架状态
   * @param {string} shelfId 货架ID
   * @param {Object} statusData 状态数据
   * @returns {Promise<void>}
   */
  async updateShelfStatus(shelfId, statusData) {
    try {
      await this.db.collection('account-shelves')
        .doc(shelfId)
        .update({
          unified_state: statusData.unified_state,
          platform_status: JSON.stringify(statusData.platform_status),
          last_sync_time: new Date(),
          update_time: new Date()
        })
    } catch (error) {
      console.error('更新货架状态失败:', error)
      throw error
    }
  }

  /**
   * 更新货架监控状态
   * @param {string} shelfId 货架ID
   * @param {boolean} isActive 是否启用监控
   * @returns {Promise<void>}
   */
  async updateShelfMonitorStatus(shelfId, isActive) {
    try {
      await this.db.collection('account-shelves')
        .doc(shelfId)
        .update({
          is_active: isActive,
          update_time: new Date()
        })
    } catch (error) {
      console.error('更新货架监控状态失败:', error)
      throw error
    }
  }

  /**
   * 获取操作日志
   * @param {string} userId 用户ID
   * @param {Object} queryOptions 查询选项
   * @returns {Promise<Object>} 日志列表和分页信息
   */
  async getOperationLogs(userId, queryOptions = {}) {
    try {
      const {
        platformType,
        action,
        status,
        pageIndex = 1,
        pageSize = 20
      } = queryOptions

      // 构建查询条件，忽略空字符串参数
      const whereCondition = { user_id: userId }

      if (platformType && platformType.trim()) {
        whereCondition.platform_type = platformType
      }

      if (action && action.trim()) {
        whereCondition.action = action
      }

      if (status !== undefined && status !== '') {
        whereCondition.status = status
      }

      // 并行执行查询和计数
      const [logs, total] = await Promise.all([
        this.db.collection('operation-logs')
          .where(whereCondition)
          .orderBy('create_time', 'desc')
          .skip((pageIndex - 1) * pageSize)
          .limit(pageSize)
          .get(),
        this.db.collection('operation-logs')
          .where(whereCondition)
          .count()
      ])

      return {
        list: logs.data,
        total: total.total,
        pageIndex,
        pageSize
      }
    } catch (error) {
      console.error('获取操作日志失败:', error)
      throw error
    }
  }

  /**
   * 清理过期日志
   * @param {number} daysToKeep 保留天数
   * @returns {Promise<void>}
   */
  async cleanupExpiredLogs(daysToKeep = 1) {
    try {
      const expireDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000)
      await this.db.collection('operation-logs')
        .where({
          create_time: this.dbCmd.lt(expireDate)
        })
        .remove()
      console.log(`清理过期日志完成，保留${daysToKeep}天`)
    } catch (error) {
      console.error('清理过期日志失败:', error)
      throw error
    }
  }

  /**
   * 清理数据库中现有的重复记录
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Array} existingShelves 现有货架记录
   * @param {Object} stats 统计信息对象
   */
  async _cleanupExistingDuplicates(userId, platformType, existingShelves, stats) {
    // 按 game_account 分组现有记录
    const accountGroups = {}
    existingShelves.forEach(shelf => {
      if (!accountGroups[shelf.game_account]) {
        accountGroups[shelf.game_account] = []
      }
      accountGroups[shelf.game_account].push(shelf)
    })

    const deletePromises = []

    for (const [gameAccount, shelves] of Object.entries(accountGroups)) {
      if (shelves.length > 1) {
        console.log(`清理数据库中重复的游戏账号 ${gameAccount}，共 ${shelves.length} 条记录`)

        // 按 last_sync_time 排序，保留最新的记录
        shelves.sort((a, b) => new Date(b.last_sync_time) - new Date(a.last_sync_time))
        const toKeep = shelves[0]
        const toDelete = shelves.slice(1)

        console.log(`保留记录 ${toKeep._id} (${toKeep.platform_shelf_id})，删除 ${toDelete.length} 条重复记录`)

        // 添加删除操作
        toDelete.forEach(shelf => {
          deletePromises.push(
            this.db.collection('account-shelves')
              .doc(shelf._id)
              .remove()
          )
          stats.duplicatesRemoved++
          console.log(`标记删除重复记录: ${shelf._id} (${shelf.platform_shelf_id})`)
        })
      }
    }

    // 执行批量删除
    if (deletePromises.length > 0) {
      await Promise.all(deletePromises)
      console.log(`清理数据库重复记录完成，删除了 ${deletePromises.length} 条记录`)

      // 重新获取清理后的数据，更新 existingShelves 引用
      const updatedShelves = await this.db.collection('account-shelves')
        .where({
          user_id: userId,
          platform_type: platformType
        })
        .get()

      // 更新原数组内容
      existingShelves.length = 0
      existingShelves.push(...updatedShelves.data)
    }
  }

  /**
   * 对货架列表进行去重处理（性能优化版本）
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Array} shelfList 原始货架列表
   * @param {Object} stats 统计信息对象
   * @param {Array} existingShelves 现有货架记录（避免重复查询）
   * @returns {Promise<Array>} 去重后的货架列表
   */
  async _deduplicateShelfList(userId, platformType, shelfList, stats, existingShelves = []) {
    // 按 game_account 分组
    const accountGroups = {}
    shelfList.forEach(shelf => {
      if (!accountGroups[shelf.game_account]) {
        accountGroups[shelf.game_account] = []
      }
      accountGroups[shelf.game_account].push(shelf)
    })

    // 构建现有记录的映射，避免重复查询数据库
    const existingShelfMap = {}
    existingShelves.forEach(shelf => {
      if (!existingShelfMap[shelf.game_account]) {
        existingShelfMap[shelf.game_account] = []
      }
      existingShelfMap[shelf.game_account].push(shelf)
    })

    const deduplicatedShelves = []

    for (const [gameAccount, shelves] of Object.entries(accountGroups)) {
      if (shelves.length === 1) {
        // 只有一个货架，直接添加
        deduplicatedShelves.push(shelves[0])
      } else {
        // 有多个货架，需要去重
        console.log(`检测到游戏账号 ${gameAccount} 有 ${shelves.length} 个重复货架，执行去重`)

        // 选择保留的货架：优先选择数据库中已存在的，否则选择第一个
        let selectedShelf = shelves[0]

        const existingRecords = existingShelfMap[gameAccount] || []
        if (existingRecords.length > 0) {
          // 按 last_sync_time 排序，获取最新的记录
          existingRecords.sort((a, b) => new Date(b.last_sync_time) - new Date(a.last_sync_time))
          const latestExisting = existingRecords[0]

          // 查找与现有记录匹配的货架
          const matchingShelf = shelves.find(s => s.id === latestExisting.platform_shelf_id)
          if (matchingShelf) {
            selectedShelf = matchingShelf
            console.log(`保留现有货架 ${latestExisting.platform_shelf_id} for account ${gameAccount}`)
          } else {
            console.log(`现有货架 ${latestExisting.platform_shelf_id} 在平台上已不存在，选择新货架 ${selectedShelf.id}`)
          }
        }

        deduplicatedShelves.push(selectedShelf)
        stats.duplicatesRemoved += shelves.length - 1

        console.log(`账号 ${gameAccount} 去重完成，保留货架 ${selectedShelf.id}，移除 ${shelves.length - 1} 个重复项`)
      }
    }

    console.log(`去重处理完成，从 ${shelfList.length} 个货架减少到 ${deduplicatedShelves.length} 个`)
    return deduplicatedShelves
  }

  /**
   * 更新或插入处理后的货架数据（性能优化版本）
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Array} processedShelves 处理后的货架列表
   * @param {Array} existingShelves 现有货架记录
   * @param {Object} stats 统计信息对象
   */
  async _upsertProcessedShelves(userId, platformType, processedShelves, existingShelves, stats) {
    if (processedShelves.length === 0) {
      console.log('没有需要处理的货架数据')
      return
    }

    // 构建现有货架的映射，提高查找效率
    const existingShelfMap = new Map()
    existingShelves.forEach(shelf => {
      existingShelfMap.set(shelf.platform_shelf_id, shelf)
    })

    // 分离更新和插入操作
    const updateOperations = []
    const insertOperations = []

    processedShelves.forEach(shelf => {
      const existingShelf = existingShelfMap.get(shelf.id)

      const shelfRecord = {
        user_id: userId,
        platform_type: platformType,
        platform_shelf_id: shelf.id,
        game_account: shelf.game_account,
        game_name: shelf.game_name,
        game_role_name: shelf.game_role_name,
        shelf_title: shelf.shelf_title,
        rent_price: shelf.rent_price,
        min_rent_time: shelf.min_rent_time,
        unified_state: shelf.unified_state,
        platform_status: JSON.stringify(shelf.platform_status),
        last_sync_time: new Date(),
        update_time: new Date()
      }

      if (existingShelf) {
        // 更新现有货架
        updateOperations.push({
          docId: existingShelf._id,
          data: shelfRecord,
          shelfId: shelf.id,
          gameAccount: shelf.game_account
        })
      } else {
        // 创建新货架
        shelfRecord.create_time = new Date()
        shelfRecord.is_active = true
        insertOperations.push({
          data: shelfRecord,
          shelfId: shelf.id,
          gameAccount: shelf.game_account
        })
      }
    })

    // 批量执行更新操作
    if (updateOperations.length > 0) {
      const updatePromises = updateOperations.map(op =>
        this.db.collection('account-shelves')
          .doc(op.docId)
          .update(op.data)
          .then(() => {
            stats.updated++
            console.log(`更新货架: ${op.shelfId} (${op.gameAccount})`)
          })
      )
      await Promise.all(updatePromises)
    }

    // 批量执行插入操作
    if (insertOperations.length > 0) {
      const insertPromises = insertOperations.map(op =>
        this.db.collection('account-shelves')
          .add(op.data)
          .then(() => {
            stats.created++
            console.log(`创建货架: ${op.shelfId} (${op.gameAccount})`)
          })
      )
      await Promise.all(insertPromises)
    }

    console.log(`批量操作完成：更新 ${updateOperations.length} 个，新增 ${insertOperations.length} 个`)
  }

  /**
   * 删除平台上已不存在的本地货架记录（性能优化版本）
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Array} processedShelves 处理后的货架列表
   * @param {Array} existingShelves 现有货架记录
   * @param {Object} stats 统计信息对象
   */
  async _deleteOrphanedShelves(userId, platformType, processedShelves, existingShelves, stats) {
    if (existingShelves.length === 0) {
      console.log('没有现有货架记录需要检查')
      return
    }

    // 获取平台返回的所有货架ID
    const platformShelfIds = new Set(processedShelves.map(shelf => shelf.id))

    // 查找需要删除的孤立货架
    const orphanedShelves = existingShelves.filter(existing =>
      !platformShelfIds.has(existing.platform_shelf_id)
    )

    if (orphanedShelves.length > 0) {
      console.log(`发现 ${orphanedShelves.length} 个孤立货架，准备批量删除`)

      // 批量删除操作
      const deletePromises = orphanedShelves.map(shelf =>
        this.db.collection('account-shelves')
          .doc(shelf._id)
          .remove()
          .then(() => {
            stats.deleted++
            console.log(`删除孤立货架: ${shelf.platform_shelf_id} (${shelf.game_account})`)
          })
          .catch(error => {
            console.error(`删除货架失败 ${shelf._id}:`, error)
            throw error
          })
      )

      await Promise.all(deletePromises)
      console.log(`批量删除完成，共删除 ${orphanedShelves.length} 个孤立货架`)
    } else {
      console.log('未发现孤立货架')
    }
  }

  /**
   * 清理重复的游戏账号记录（独立方法，可单独调用）
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型（可选，不传则清理所有平台）
   * @returns {Promise<Object>} 清理结果统计
   */
  async cleanupDuplicateAccounts(userId, platformType = null) {
    try {
      const whereCondition = { user_id: userId }
      if (platformType) {
        whereCondition.platform_type = platformType
      }

      // 获取所有货架记录
      const allShelves = await this.db.collection('account-shelves')
        .where(whereCondition)
        .orderBy('last_sync_time', 'desc')
        .get()

      console.log(`开始清理重复账号，共 ${allShelves.data.length} 条记录`)

      // 按平台和游戏账号分组
      const groups = {}
      allShelves.data.forEach(shelf => {
        const key = `${shelf.platform_type}:${shelf.game_account}`
        if (!groups[key]) {
          groups[key] = []
        }
        groups[key].push(shelf)
      })

      let duplicatesRemoved = 0
      const deletePromises = []

      // 处理每个分组
      for (const [key, shelves] of Object.entries(groups)) {
        if (shelves.length > 1) {
          console.log(`发现重复账号 ${key}，共 ${shelves.length} 条记录`)

          // 保留最新的记录（按 last_sync_time 排序后的第一个）
          const toKeep = shelves[0]
          const toDelete = shelves.slice(1)

          console.log(`保留记录 ${toKeep._id}，删除 ${toDelete.length} 条重复记录`)

          // 添加删除操作到批量处理队列
          toDelete.forEach(shelf => {
            deletePromises.push(
              this.db.collection('account-shelves')
                .doc(shelf._id)
                .remove()
            )
            duplicatesRemoved++
          })
        }
      }

      // 执行批量删除
      if (deletePromises.length > 0) {
        await Promise.all(deletePromises)
        console.log(`清理完成，删除了 ${duplicatesRemoved} 条重复记录`)
      } else {
        console.log('未发现重复记录')
      }

      return {
        totalRecords: allShelves.data.length,
        duplicatesRemoved: duplicatesRemoved,
        cleanGroups: Object.keys(groups).length
      }
    } catch (error) {
      console.error('清理重复账号失败:', error)
      throw error
    }
  }

  /**
   * 初始化同步统计信息
   * @returns {Object} 统计信息对象
   */
  _initializeSyncStats() {
    // {{ AURA-X: Add - 提取统计信息初始化逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}
    return {
      updated: 0,
      created: 0,
      deleted: 0,
      duplicatesRemoved: 0,
      performance: {}
    }
  }

  /**
   * 获取现有货架数据
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Object} stats 统计信息对象
   * @returns {Object} 现有货架数据
   */
  async _fetchExistingShelves(userId, platformType, stats) {
    // {{ AURA-X: Add - 提取现有货架数据获取逻辑，统一性能统计. Approval: 寸止(ID:**********). }}
    const stepStart = Date.now()
    const existingShelves = await this.db.collection('account-shelves')
      .where({
        user_id: userId,
        platform_type: platformType
      })
      .get()
    stats.performance.fetchExisting = Date.now() - stepStart

    console.log(`数据库中现有 ${existingShelves.data.length} 个货架记录 (${stats.performance.fetchExisting}ms)`)
    return existingShelves.data
  }

  /**
   * 执行同步操作
   * @param {string} userId 用户ID
   * @param {string} platformType 平台类型
   * @param {Array} shelfList 货架列表
   * @param {Array} existingShelves 现有货架数据
   * @param {Object} stats 统计信息对象
   */
  async _performSyncOperations(userId, platformType, shelfList, existingShelves, stats) {
    // {{ AURA-X: Add - 提取同步操作执行逻辑，简化主函数. Approval: 寸止(ID:**********). }}
    let stepStart = Date.now()

    // 清理数据库中的重复记录
    await this._cleanupExistingDuplicates(userId, platformType, existingShelves, stats)
    stats.performance.cleanupDuplicates = Date.now() - stepStart

    // 处理平台返回的货架数据，实现去重逻辑
    stepStart = Date.now()
    const processedShelves = await this._deduplicateShelfList(userId, platformType, shelfList, stats, existingShelves)
    stats.performance.deduplicateList = Date.now() - stepStart

    // 更新或插入处理后的货架数据
    stepStart = Date.now()
    await this._upsertProcessedShelves(userId, platformType, processedShelves, existingShelves, stats)
    stats.performance.upsertShelves = Date.now() - stepStart

    // 删除平台上已不存在的本地货架记录
    stepStart = Date.now()
    await this._deleteOrphanedShelves(userId, platformType, processedShelves, existingShelves, stats)
    stats.performance.deleteOrphaned = Date.now() - stepStart
  }
}

module.exports = DatabaseManager

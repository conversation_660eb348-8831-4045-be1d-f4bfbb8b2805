'use strict'

// {{ AURA-X: Modify - 移除直接数据库操作，统一使用DatabaseManager. Approval: 寸止(ID:**********). }}
const db = uniCloud.database() // 仅用于用户token验证
const dbCmd = db.command

// {{ AURA-X: Modify - 使用公共模块替代重复代码. Approval: 寸止(ID:1735372800). }}
const { PlatformAdapterFactory, StateManager, Logger, DatabaseManager } = require('shelf-core')

// {{ AURA-X: Modify - 添加响应状态码常量，提高代码可读性. Approval: 寸止(ID:1735373400). }}
/**
 * 系统常量定义
 */
const RESPONSE_CODES = {
  SUCCESS: 0,           // 操作成功
  GENERAL_ERROR: -1,    // 一般错误
  AUTH_ERROR: -2        // 认证错误（未登录）
}

const QUERY_CONSTANTS = {
  MAX_SHELVES_PER_QUERY: 100    // 单次查询最大货架数量
}

/**
 * 错误响应生成器
 *
 * 提供统一的错误响应格式和错误分类功能
 */
class ResponseHelper {
  /**
   * 创建成功响应
   * @param {*} data - 响应数据
   * @param {string} message - 成功消息
   * @returns {Object} 成功响应对象
   */
  static createSuccessResponse(data = null, message = '操作成功') {
    // {{ AURA-X: Add - 统一成功响应格式. Approval: 寸止(ID:**********). }}
    return {
      code: RESPONSE_CODES.SUCCESS,
      data,
      message
    }
  }

  /**
   * 创建错误响应
   * @param {number} code - 错误代码
   * @param {string} message - 错误消息
   * @param {Error} error - 原始错误对象（可选）
   * @returns {Object} 错误响应对象
   */
  static createErrorResponse(code, message, error = null) {
    // {{ AURA-X: Add - 统一错误响应格式，支持错误分类. Approval: 寸止(ID:**********). }}
    const response = {
      code,
      message
    }

    // 在开发环境中包含详细错误信息
    if (error && process.env.NODE_ENV === 'development') {
      response.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    }

    return response
  }

  /**
   * 记录并创建错误响应
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   * @returns {Object} 错误响应对象
   */
  static logAndCreateErrorResponse(error, context) {
    // {{ AURA-X: Add - 错误日志记录和响应生成. Approval: 寸止(ID:**********). }}
    console.error(`[${context}] 操作失败:`, error)

    // 根据错误类型返回不同的错误码
    let code = RESPONSE_CODES.GENERAL_ERROR
    let message = error.message || '操作失败'

    // 特定错误类型的处理
    if (message.includes('未登录') || message.includes('token')) {
      code = RESPONSE_CODES.AUTH_ERROR
    }

    return this.createErrorResponse(code, message, error)
  }
}

/**
 * 验证用户身份令牌并获取用户ID
 *
 * 通过检查token的有效性和过期时间来验证用户身份
 *
 * @param {string} token - 用户身份令牌
 * @returns {string|null} 用户ID或null（验证失败）
 */
async function verifyToken(token) {
  // 检查token是否存在
  if (!token) {
    return null
  }

  try {
    // 在用户表中查找有效的token
    const userQueryResult = await db.collection('uni-id-users')
      .where({
        'token.token': token,                    // token值匹配
        'token.expire': dbCmd.gt(new Date()),   // token未过期
        status: 0                               // 用户状态正常
      })
      .get()

    // 如果找到有效用户，返回用户ID
    if (userQueryResult.data.length > 0) {
      return userQueryResult.data[0]._id
    }

    return null
  } catch (error) {
    console.error('验证用户token失败:', error)
    return null
  }
}

/**
 * 货架管理云函数主入口
 *
 * 提供客户端调用的各种货架管理接口，包括：
 * - 平台配置管理
 * - 货架数据同步
 * - 货架状态控制
 * - 操作日志查询
 *
 * @param {Object} event - 云函数事件对象
 * @param {string} event.action - 操作类型
 * @param {Object} event.data - 请求数据
 * @param {string} event._token - 用户身份令牌
 * @param {Object} context - 云函数上下文对象
 * @returns {Object} 操作结果
 */
exports.main = async (event, context) => {
  const { action, data, _token } = event

  // {{ AURA-X: Modify - 简化主函数逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}

  // 步骤1：验证用户身份
  const authenticatedUserId = await verifyToken(_token)
  if (!authenticatedUserId) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.AUTH_ERROR, '用户未登录或token已过期')
  }

  try {
    // 步骤2：执行对应的业务操作
    const operationResult = await executeAction(action, authenticatedUserId, data)
    return operationResult

  } catch (error) {
    // {{ AURA-X: Modify - 使用统一错误处理器处理云函数级错误. Approval: 寸止(ID:**********). }}
    return ResponseHelper.logAndCreateErrorResponse(error, `货架管理-${action}`)
  }
}
/**
 * 获取系统支持的平台列表
 *
 * 返回当前系统支持的所有平台信息，包括平台类型、名称、功能特性等
 *
 * @returns {Object} 包含平台列表的响应对象
 */
async function getPlatformList() {
  try {
    const supportedPlatforms = PlatformAdapterFactory.getSupportedPlatforms()

    return ResponseHelper.createSuccessResponse(supportedPlatforms, '获取平台列表成功')
  } catch (error) {
    return ResponseHelper.logAndCreateErrorResponse(error, '获取平台列表')
  }
}

/**
 * 保存或更新平台配置
 *
 * 为用户保存平台的登录配置信息，包括账号密码、token等
 *
 * @param {string} userId - 用户ID
 * @param {Object} configData - 平台配置数据
 * @returns {Object} 保存结果响应对象
 */
async function savePlatformConfig(userId, configData) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const databaseManager = new DatabaseManager()
    await databaseManager.savePlatformConfig(userId, configData)

    return {
      code: RESPONSE_CODES.SUCCESS,
      message: '保存成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 获取用户的平台配置列表
 */
async function getPlatformConfigs(userId) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const configs = await dbManager.getPlatformConfigs(userId, null, true)

    // 隐藏敏感信息
    const safeConfigs = configs.map(config => {
      delete config.password
      delete config.token
      delete config.cookie
      delete config.headers
      return config
    })

    return {
      code: 0,
      data: safeConfigs,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 删除平台配置
 */
async function deletePlatformConfig(userId, configId) {
  try {
    await db.collection('platform-configs')
      .where({
        _id: configId,
        user_id: userId
      })
      .update({
        status: 0,
        update_time: new Date()
      })
    return {
      code: 0,
      message: '删除成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 测试平台登录
 */
async function testPlatformLogin(userId, configData) {
  try {
    console.log('开始测试平台登录')
    console.log('用户ID:', userId)
    console.log('前端传递的配置数据:', configData)

    // {{ AURA-X: Modify - 使用配置ID精确获取配置，避免误操作多个同类型配置. Approval: 寸止(ID:1735373000). }}
    const dbManager = new DatabaseManager()
    let finalConfig = { ...configData }

    // 如果提供了配置ID，从数据库获取完整配置信息
    if (configData.configId) {
      // {{ AURA-X: Fix - 修复uniCloud数据库查询语法错误. Approval: 寸止(ID:1735373100). }}
      const result = await db.collection('platform-configs')
        .where({
          _id: configData.configId,
          user_id: userId // 确保安全性，只能操作自己的配置
        })
        .get()

      if (result.data.length > 0) {
        const existingConfig = result.data[0]
        console.log('找到数据库中的配置')

        // 使用数据库中的真实数据
        finalConfig.username = existingConfig.username
        finalConfig.password = existingConfig.password
        finalConfig.token = existingConfig.token
        finalConfig.cookie = existingConfig.cookie
        finalConfig.headers = existingConfig.headers || {}
        finalConfig._id = existingConfig._id // 保存配置ID用于后续更新
      }
    } else {
      // 兼容旧版本：如果没有配置ID，使用原有逻辑
      const dbConfigs = await dbManager.getPlatformConfigs(userId, configData.platformType, false)
      if (dbConfigs.length > 0) {
        const existingConfig = dbConfigs[0]
        console.log('找到数据库中的配置（兼容模式）')

        finalConfig.username = existingConfig.username
        finalConfig.password = existingConfig.password
        finalConfig.token = existingConfig.token
        finalConfig.cookie = existingConfig.cookie
        finalConfig.headers = existingConfig.headers || {}
        finalConfig._id = existingConfig._id
      }
    }

    console.log('最终使用的配置数据:', {
      configId: finalConfig._id,
      platformType: finalConfig.platformType,
      username: finalConfig.username,
      hasPassword: !!finalConfig.password,
      hasToken: !!finalConfig.token,
      hasCookie: !!finalConfig.cookie
    })

    const adapter = PlatformAdapterFactory.create(finalConfig.platformType, {
      ...finalConfig,
      user_id: userId
    })

    console.log('适配器创建成功，开始登录')
    const loginResult = await adapter.login()
    console.log('登录结果:', loginResult)

    // {{ AURA-X: Delete - 移除云函数层的重复数据库更新，适配器内部已处理. Approval: 寸止(ID:1735373000). }}
    // 适配器内部已经处理了数据库更新，这里不再重复更新

    return {
      code: loginResult.success ? 0 : -1,
      message: loginResult.message
    }
  } catch (error) {
    console.error('测试平台登录异常:', error)
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 更新货架数据
 */
async function syncShelves(userId, platformType) {
  try {
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // 获取平台配置
    const dbManager = new DatabaseManager()
    const platformConfigs = await dbManager.getPlatformConfigs(userId, platformType, true)

    if (platformConfigs.length === 0) {
      throw new Error('未找到平台配置')
    }
    const config = platformConfigs[0]
    const adapter = PlatformAdapterFactory.create(platformType, config)
    // {{ AURA-X: Modify - 优化登录状态检查，登录状态检测已移至统一的API响应处理中. Approval: 寸止(ID:**********). }}
    // 检查基本登录状态（token是否存在）
    const isLoggedIn = await adapter.checkLoginStatus()
    if (!isLoggedIn) {
      // 尝试自动登录
      if (config.auto_login) {
        const loginResult = await adapter.login()
        if (!loginResult.success) {
          throw new Error('登录失败: ' + loginResult.message)
        }
      } else {
        throw new Error('平台未登录，请手动更新Cookie')
      }
    }
    // 获取货架列表
    const shelfList = await adapter.getShelfList()
    // {{ AURA-X: Modify - 重构批量货架操作，支持去重和删除检测. Approval: 寸止(ID:1735373200). }}
    // 批量更新或插入货架数据（支持去重和删除检测）
    const syncResult = await dbManager.batchUpsertShelves(userId, platformType, shelfList)

    // 构建详细的同步消息
    const { stats } = syncResult
    const details = []
    if (stats.created > 0) details.push(`新增${stats.created}个`)
    if (stats.updated > 0) details.push(`更新${stats.updated}个`)
    if (stats.deleted > 0) details.push(`删除${stats.deleted}个`)
    if (stats.duplicatesRemoved > 0) details.push(`去重${stats.duplicatesRemoved}个`)

    const message = `同步完成，${details.join('，')}货架`

    // 记录更新日志
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'sync',
      status: 1,
      message: message,
      trigger_type: 'manual'
    })

    return {
      code: 0,
      data: {
        syncCount: syncResult.total,
        stats: stats,
        details: message
      },
      message: message
    }
  } catch (error) {
    const logger = new Logger()
    await logger.log({
      user_id: userId,
      platform_type: platformType,
      action: 'sync',
      status: 0,
      message: error.message,
      trigger_type: 'manual'
    })
    return {
      code: -1,
      message: error.message
    }
  }
}

/**
 * 清理重复的游戏账号记录
 */
async function cleanupDuplicateAccounts(userId, platformType) {
  try {
    const logger = new Logger()
    const dbManager = new DatabaseManager()

    // 执行清理操作
    const cleanupResult = await dbManager.cleanupDuplicateAccounts(userId, platformType)

    // 记录清理日志
    await logger.log({
      user_id: userId,
      platform_type: platformType || 'all',
      action: 'cleanup_duplicates',
      status: 1,
      message: `清理完成，删除了 ${cleanupResult.duplicatesRemoved} 条重复记录`,
      trigger_type: 'manual'
    })

    return {
      code: 0,
      data: cleanupResult,
      message: `清理完成，删除了 ${cleanupResult.duplicatesRemoved} 条重复记录`
    }
  } catch (error) {
    const logger = new Logger()
    await logger.log({
      user_id: userId,
      platform_type: platformType || 'all',
      action: 'cleanup_duplicates',
      status: 0,
      message: error.message,
      trigger_type: 'manual'
    })
    return {
      code: -1,
      message: error.message
    }
  }
}

/**
 * 获取货架列表
 */
async function getShelfList(userId, queryData = {}) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const result = await dbManager.getShelfList(userId, queryData)

    return {
      code: 0,
      data: result,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 手动切换货架状态
 */
async function toggleShelfStatus(userId, shelfData) {
  try {
    const { shelfId, targetStatus } = shelfData
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架信息
    const shelfResult = await dbManager.getShelfList(userId, { pageSize: QUERY_CONSTANTS.MAX_SHELVES_PER_QUERY })
    const shelfInfo = shelfResult.list.find(s => s._id === shelfId)

    if (!shelfInfo) {
      throw new Error('货架不存在')
    }

    // 获取平台配置
    const platformConfigs = await dbManager.getPlatformConfigs(userId, shelfInfo.platform_type, true)
    if (platformConfigs.length === 0) {
      throw new Error('平台配置不存在')
    }
    const config = platformConfigs[0]
    const adapter = PlatformAdapterFactory.create(shelfInfo.platform_type, config)
    // 验证目标状态
    if (!StateManager.isValidState(targetStatus)) {
      throw new Error('无效的目标状态')
    }

    // 执行上下架操作
    let result
    let action
    if (targetStatus === StateManager.STATES.AVAILABLE) {
      // 上架
      result = await adapter.onShelf(shelfInfo.platform_shelf_id)
      action = 'on_shelf'
    } else if (targetStatus === StateManager.STATES.OFFLINE) {
      // 下架
      result = await adapter.offShelf(shelfInfo.platform_shelf_id)
      action = 'off_shelf'
    } else {
      throw new Error('不支持的目标状态')
    }
    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: shelfInfo.platform_type,
      platform_shelf_id: shelfInfo.platform_shelf_id,
      game_account: shelfInfo.game_account,
      action: action,
      status: result.success ? 1 : 0,
      message: result.message,
      trigger_type: 'manual'
    })
    if (result.success) {
      // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
      // 更新本地状态
      await dbManager.updateShelfStatus(shelfId, {
        unified_state: targetStatus,
        platform_status: {}
      })
    }
    return {
      code: result.success ? 0 : -1,
      message: result.message
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
/**
 * 切换货架监控状态
 */
async function toggleMonitorStatus(userId, shelfData) {
  try {
    const { shelfId, isActive } = shelfData
    const logger = new Logger()
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()

    // 获取货架信息
    const shelfResult = await dbManager.getShelfList(userId, { pageSize: QUERY_CONSTANTS.MAX_SHELVES_PER_QUERY })
    const shelfInfo = shelfResult.list.find(s => s._id === shelfId)

    if (!shelfInfo) {
      throw new Error('货架不存在')
    }

    // 更新监控状态
    await dbManager.updateShelfMonitorStatus(shelfId, isActive)
    
    // 记录操作日志
    await logger.log({
      user_id: userId,
      platform_type: shelfInfo.platform_type,
      platform_shelf_id: shelfInfo.platform_shelf_id,
      game_account: shelfInfo.game_account,
      action: isActive ? 'enable_monitor' : 'disable_monitor',
      status: 1,
      message: isActive ? '开启监控' : '关闭监控',
      trigger_type: 'manual'
    })
    
    return {
      code: 0,
      message: isActive ? '已开启监控' : '已关闭监控'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}
// {{ AURA-X: Remove - 已被ResponseHelper.createErrorResponse替代. Approval: 寸止(ID:**********). }}
// 原createErrorResponse函数已被ResponseHelper.createErrorResponse替代

/**
 * 执行动作处理
 * @param {string} action 动作类型
 * @param {string} uid 用户ID
 * @param {Object} data 请求数据
 * @returns {Object} 处理结果
 */
async function executeAction(action, uid, data) {
  // {{ AURA-X: Add - 使用动作处理器映射替代长switch语句，提高可维护性. Approval: 寸止(ID:**********). }}
  const actionHandlers = {
    'getPlatformList': () => getPlatformList(),
    'savePlatformConfig': () => savePlatformConfig(uid, data),
    'getPlatformConfigs': () => getPlatformConfigs(uid),
    'deletePlatformConfig': () => deletePlatformConfig(uid, data.configId),
    'testPlatformLogin': () => testPlatformLogin(uid, data),
    'syncShelves': () => syncShelves(uid, data.platformType),
    'getShelfList': () => getShelfList(uid, data),
    'toggleShelfStatus': () => toggleShelfStatus(uid, data),
    'toggleMonitorStatus': () => toggleMonitorStatus(uid, data),
    'getOperationLogs': () => getOperationLogs(uid, data),
    'cleanupDuplicates': () => cleanupDuplicateAccounts(uid, data.platformType)
  }

  const handler = actionHandlers[action]
  if (!handler) {
    return ResponseHelper.createErrorResponse(RESPONSE_CODES.GENERAL_ERROR, '不支持的操作')
  }

  return await handler()
}

/**
 * 获取操作日志
 */
async function getOperationLogs(userId, queryData = {}) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    const result = await dbManager.getOperationLogs(userId, queryData)

    return {
      code: 0,
      data: result,
      message: '获取成功'
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message
    }
  }
}

'use strict'

// 导入各平台适配器
const <PERSON><PERSON><PERSON>WanAdapter = require('./adapters/zuhaowan-adapter')
const UhaoZuAdapter = require('./adapters/uhaozu-adapter')

/**
 * 支持的平台配置定义
 */
const SUPPORTED_PLATFORMS = [
  {
    type: 'zuhaowan',
    name: '租号玩',
    autoLogin: true,
    description: '支持账号密码自动登录',
    features: ['自动登录', '货架管理', '状态同步', '联动上下架']
  },
  {
    type: 'uhao<PERSON>',
    name: 'U号租',
    autoLogin: true,
    description: '支持账号密码自动登录',
    features: ['自动登录', '货架管理', '状态同步', '联动上下架']
  }
]

/**
 * 平台适配器映射表
 */
const ADAPTER_MAPPING = {
  'zuhaowan': ZuhaoWanAdapter,
  'uhaozu': UhaoZuAdapter
}

/**
 * 平台适配器工厂类
 *
 * 负责根据平台类型创建对应的适配器实例，提供统一的平台管理接口
 * 采用工厂模式，便于扩展新的平台支持
 */
class PlatformAdapterFactory {
  /**
   * 创建平台适配器实例
   *
   * @param {string} platformType - 平台类型标识
   * @param {Object} config - 平台配置信息
   * @returns {Object} 平台适配器实例
   * @throws {Error} 不支持的平台类型时抛出错误
   */
  static create(platformType, config) {
    // {{ AURA-X: Modify - 使用映射表简化适配器创建逻辑. Approval: 寸止(ID:1735373600). }}
    const AdapterClass = ADAPTER_MAPPING[platformType]

    if (!AdapterClass) {
      throw new Error(`不支持的平台类型: ${platformType}。支持的平台: ${Object.keys(ADAPTER_MAPPING).join(', ')}`)
    }

    return new AdapterClass(config)
  }

  /**
   * 获取支持的平台类型列表
   *
   * @returns {Array} 支持的平台类型数组
   */
  static getSupportedPlatforms() {
    // {{ AURA-X: Modify - 使用常量定义的平台配置. Approval: 寸止(ID:1735373600). }}
    return [...SUPPORTED_PLATFORMS] // 返回副本，避免外部修改
  }

  /**
   * 检查平台类型是否支持
   *
   * @param {string} platformType - 平台类型
   * @returns {boolean} 是否支持
   */
  static isSupported(platformType) {
    // {{ AURA-X: Modify - 直接使用映射表检查，提高性能. Approval: 寸止(ID:1735373600). }}
    return ADAPTER_MAPPING.hasOwnProperty(platformType)
  }

  /**
   * 获取平台详细信息
   *
   * @param {string} platformType - 平台类型
   * @returns {Object|null} 平台详细信息，不存在时返回null
   */
  static getPlatformInfo(platformType) {
    // {{ AURA-X: Add - 新增获取平台详细信息的方法. Approval: 寸止(ID:1735373600). }}
    return SUPPORTED_PLATFORMS.find(platform => platform.type === platformType) || null
  }

  /**
   * 获取所有支持的平台类型
   *
   * @returns {Array} 平台类型数组
   */
  static getSupportedPlatformTypes() {
    // {{ AURA-X: Add - 新增获取平台类型列表的便捷方法. Approval: 寸止(ID:1735373600). }}
    return Object.keys(ADAPTER_MAPPING)
  }

  /**
   * 验证平台配置
   *
   * @param {string} platformType - 平台类型
   * @param {Object} config - 平台配置
   * @returns {Object} 验证结果 {valid: boolean, errors: Array}
   */
  static validateConfig(platformType, config) {
    // {{ AURA-X: Add - 新增配置验证功能. Approval: 寸止(ID:1735373600). }}
    const errors = []

    // 检查平台类型是否支持
    if (!PlatformAdapterFactory.isSupported(platformType)) {
      errors.push(`不支持的平台类型: ${platformType}`)
    }

    // 检查必要的配置项
    if (!config) {
      errors.push('缺少平台配置信息')
    } else {
      if (!config.username) {
        errors.push('缺少用户名配置')
      }
      if (!config.password) {
        errors.push('缺少密码配置')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

module.exports = PlatformAdapterFactory
